package com.gnico.majo.application.usecase

import com.gnico.majo.application.port.`in`.PrintService
import com.gnico.majo.application.port.`in`.VentaParaReimpresionInfo
import com.gnico.majo.application.port.`in`.ComprobanteParaReimpresionInfo
import com.gnico.majo.application.port.`in`.ComprobanteBasicoInfo
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.application.port.out.PrinterPort
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.text.DecimalFormat

class PrintServiceImpl(
    private val saleRepository: SaleRepositoryPort,
    private val printer: PrinterPort
) : PrintService {
    
    private val dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")
    private val inputDateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    private val decimalFormatter = DecimalFormat("#,##0.00")
    
    override fun imprimirTicket(ventaId: Id, comprobanteId: Id?) {
        val sale = saleRepository.findSaleById(ventaId)
            ?: throw IllegalArgumentException("Venta ${ventaId.value} no encontrada")

        val comprobante = if (comprobanteId != null) {
            saleRepository.findComprobanteById(comprobanteId)
                ?: throw IllegalArgumentException("Comprobante ${comprobanteId.value} no encontrado")
        } else {
            null
        }

        printer.printTicket(comprobante, sale)
    }
    
    override fun buscarVentasParaReimpresion(
        numeroVenta: String?,
        fechaDesde: String?,
        fechaHasta: String?,
        usuario: String?
    ): List<VentaParaReimpresionInfo> {
        
        val ventas = when {
            // Búsqueda por número de venta específico
            !numeroVenta.isNullOrBlank() -> {
                val venta = saleRepository.findSaleByNumeroVenta(numeroVenta)
                if (venta != null) listOf(venta) else emptyList()
            }
            
            // Búsqueda por usuario
            !usuario.isNullOrBlank() -> {
                saleRepository.findSalesByUsuario(usuario)
            }
            
            // Búsqueda por rango de fechas
            !fechaDesde.isNullOrBlank() && !fechaHasta.isNullOrBlank() -> {
                val startDate = LocalDateTime.parse(fechaDesde, inputDateFormatter)
                val endDate = LocalDateTime.parse(fechaHasta, inputDateFormatter)
                saleRepository.findSalesByDateRange(startDate, endDate)
            }
            
            // Si no hay criterios específicos, devolver lista vacía
            else -> emptyList()
        }
        
        return ventas.map { venta ->
            val comprobantes = saleRepository.findComprobantesByVentaId(
                venta.id ?: throw IllegalStateException("Venta sin ID")
            )
            
            VentaParaReimpresionInfo(
                ventaId = venta.id!!,
                numeroVenta = venta.numeroVenta,
                fechaVenta = venta.fechaVenta.format(dateFormatter),
                clienteNombre = venta.cliente?.nombre,
                usuarioNombre = venta.usuario.nombreDisplay,
                montoTotal = decimalFormatter.format(venta.montoTotal),
                medioPago = venta.medioPago,
                tieneComprobante = venta.comprobanteEmitido,
                comprobantes = comprobantes.map { comprobante ->
                    ComprobanteBasicoInfo(
                        comprobanteId = comprobante.id ?: throw IllegalStateException("Comprobante sin ID"),
                        tipoComprobante = comprobante.tipoComprobante,
                        puntoVenta = comprobante.puntoVenta,
                        numeroComprobante = comprobante.numeroComprobante,
                        estado = comprobante.estado
                    )
                }
            )
        }
    }
    
    override fun buscarComprobantesParaReimpresion(
        puntoVenta: Int?,
        numeroComprobante: Int?,
        fechaDesde: String?,
        fechaHasta: String?
    ): List<ComprobanteParaReimpresionInfo> {
        
        // Búsqueda por número específico de comprobante
        if (puntoVenta != null && numeroComprobante != null) {
            val comprobante = saleRepository.findComprobanteByNumero(puntoVenta, numeroComprobante)
            return if (comprobante != null) {
                val sale = saleRepository.findSaleById(comprobante.venta)
                    ?: throw IllegalStateException("Venta ${comprobante.venta.value} no encontrada")
                
                listOf(
                    ComprobanteParaReimpresionInfo(
                        comprobanteId = comprobante.id ?: throw IllegalStateException("Comprobante sin ID"),
                        ventaId = comprobante.venta,
                        numeroVenta = sale.numeroVenta,
                        tipoComprobante = comprobante.tipoComprobante,
                        puntoVenta = comprobante.puntoVenta,
                        numeroComprobante = comprobante.numeroComprobante,
                        cae = comprobante.cae,
                        fechaEmision = comprobante.fechaEmision.format(dateFormatter),
                        clienteNombre = sale.cliente?.nombre,
                        usuarioNombre = sale.usuario.nombreDisplay,
                        montoTotal = decimalFormatter.format(comprobante.impTotal),
                        estado = comprobante.estado
                    )
                )
            } else {
                emptyList()
            }
        }
        
        // Para búsquedas por fecha, necesitaríamos implementar métodos adicionales en el repositorio
        // Por ahora retornamos lista vacía para estos casos
        return emptyList()
    }
}
