package com.gnico.majo.adapter.controller.rest

import com.gnico.majo.application.port.`in`.PrintService
import com.gnico.majo.adapter.controller.dto.VentaParaReimpresionInfoResponse
import com.gnico.majo.adapter.controller.dto.ComprobanteParaReimpresionInfoResponse
import com.gnico.majo.adapter.controller.dto.toResponse
import com.gnico.majo.application.domain.model.Id
import kotlinx.serialization.Serializable

class PrintController(
    private val printService: PrintService
) {
    
    /**
     * Imprime un ticket térmico unificado
     * @param ventaId ID de la venta
     * @param comprobanteId ID del comprobante (opcional, null para ticket genérico)
     */
    fun imprimirTicket(ventaId: Int, comprobanteId: Int? = null): PrintResponse {
        return try {
            val comprobanteIdObj = comprobanteId?.let { Id(it) }
            printService.imprimirTicket(Id(ventaId), comprobanteIdObj)

            val message = if (comprobanteId != null) {
                "Ticket fiscal impreso exitosamente"
            } else {
                "Ticket de venta impreso exitosamente"
            }

            PrintResponse(
                success = true,
                message = message
            )
        } catch (e: Exception) {
            PrintResponse(
                success = false,
                message = "Error al imprimir ticket",
                error = e.message
            )
        }
    }

    // Métodos de compatibilidad hacia atrás (delegando al método unificado)

    /**
     * Imprime un ticket genérico para una venta (sin comprobante fiscal)
     * @deprecated Usar imprimirTicket(ventaId, null) en su lugar
     */
    fun imprimirTicketVenta(ventaId: Int): PrintResponse {
        return imprimirTicket(ventaId, null)
    }

    /**
     * Imprime un ticket de comprobante fiscal
     * @deprecated Usar imprimirTicket(ventaId, comprobanteId) en su lugar
     */
    fun imprimirTicketComprobante(comprobanteId: Int): PrintResponse {
        // Para mantener compatibilidad, necesitamos obtener el ventaId del comprobante
        return try {
            // Buscar el comprobante para obtener el ventaId
            val comprobantes = printService.buscarComprobantesParaReimpresion(
                puntoVenta = null,
                numeroComprobante = null,
                fechaDesde = null,
                fechaHasta = null
            ).filter { it.comprobanteId.value == comprobanteId }

            if (comprobantes.isNotEmpty()) {
                val comprobante = comprobantes.first()
                imprimirTicket(comprobante.ventaId.value, comprobanteId)
            } else {
                PrintResponse(
                    success = false,
                    message = "Comprobante no encontrado",
                    error = "No se encontró el comprobante con ID $comprobanteId"
                )
            }
        } catch (e: Exception) {
            PrintResponse(
                success = false,
                message = "Error al imprimir ticket de comprobante",
                error = e.message
            )
        }
    }
    
    /**
     * Busca ventas por diferentes criterios para reimpresión
     */
    fun buscarVentasParaReimpresion(request: BuscarVentasRequest): List<VentaParaReimpresionInfoResponse> {
        return printService.buscarVentasParaReimpresion(
            numeroVenta = request.numeroVenta,
            fechaDesde = request.fechaDesde,
            fechaHasta = request.fechaHasta,
            usuario = request.usuario
        ).map { it.toResponse() }
    }

    /**
     * Busca comprobantes por diferentes criterios para reimpresión
     */
    fun buscarComprobantesParaReimpresion(request: BuscarComprobantesRequest): List<ComprobanteParaReimpresionInfoResponse> {
        return printService.buscarComprobantesParaReimpresion(
            puntoVenta = request.puntoVenta,
            numeroComprobante = request.numeroComprobante,
            fechaDesde = request.fechaDesde,
            fechaHasta = request.fechaHasta
        ).map { it.toResponse() }
    }
}

@Serializable
data class PrintResponse(
    val success: Boolean,
    val message: String,
    val error: String? = null
)

@Serializable
data class BuscarVentasRequest(
    val numeroVenta: String? = null,
    val fechaDesde: String? = null,
    val fechaHasta: String? = null,
    val usuario: String? = null
)

@Serializable
data class BuscarComprobantesRequest(
    val puntoVenta: Int? = null,
    val numeroComprobante: Int? = null,
    val fechaDesde: String? = null,
    val fechaHasta: String? = null
)
