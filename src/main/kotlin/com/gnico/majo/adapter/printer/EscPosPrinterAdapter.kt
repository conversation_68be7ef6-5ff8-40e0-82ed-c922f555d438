package com.gnico.majo.adapter.printer

import com.gnico.majo.application.domain.model.Comprobante
import com.gnico.majo.application.domain.model.Sale
import com.gnico.majo.application.port.out.PrinterPort

class EscPosPrinterAdapter : PrinterPort {
    override fun printTicket(comprobante: Comprobante?, sale: Sale) {
        if (comprobante != null) {
            println("*prints ticket fiscal para venta ${sale.numeroVenta} - comprobante ${comprobante.numeroComprobante}*")
            // Aquí iría la lógica para imprimir ticket con datos fiscales:
            // - Datos del comprobante (CAE, CUIT, etc.)
            // - Información de la venta
            // - Formato térmico unificado
        } else {
            println("*prints ticket genérico para venta ${sale.numeroVenta}*")
            // Aquí iría la lógica para imprimir ticket sin datos fiscales:
            // - Solo información de la venta
            // - Formato térmico básico
        }
    }
}